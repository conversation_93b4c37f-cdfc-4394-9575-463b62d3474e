{"version": 4, "terraform_version": "1.12.1", "serial": 3, "lineage": "95618f04-8dd9-ec0a-2c70-786955a84996", "outputs": {}, "resources": [{"mode": "managed", "type": "aws_s3_bucket", "name": "vprofile_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::vprofile-bucket-very-new", "bucket": "vprofile-bucket-very-new", "bucket_domain_name": "vprofile-bucket-very-new.s3.amazonaws.com", "bucket_prefix": "", "bucket_region": "eu-central-1", "bucket_regional_domain_name": "vprofile-bucket-very-new.s3.eu-central-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "f0160461ee99de5b8008c79a959ebe2a2a9e60a662735dfaf40f65789840f6be", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z21DNDUVLTQW6Q", "id": "vprofile-bucket-very-new", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "eu-central-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Name": "vprofile-bucket-very-new"}, "tags_all": {"Name": "vprofile-bucket-very-new"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "bucket": "vprofile-bucket-very-new", "region": "eu-central-1"}, "private": "********************************************************************************************************************************************************************************"}]}], "check_results": null}