#!/bin/bash
# shellcheck disable=SC1091
source "./functions.sh"
source "./variables.env"

# intialize s3 bucket
print_header "Initializing s3 bucket"
cd "s3_bucket" || die "Failed to change directory to s3_bucket"
terraform init || die "Failed to initialize Terraform in s3_bucket"
terraform apply -auto-approve || die "Failed to apply Terraform in s3_bucket"
cd ..

# download tooplate template
# shellcheck disable=SC2154
if [[ $download_template -eq 1 ]]; then
    print_header "Downloading tooplate template"
    bash "./download_tooplate_template.sh" "$tooplate_template_url" || die "Failed to download tooplate template"
else
    print_header "Skipping downloading tooplate template"
fi

## upload files to s3 bucket
echo "##### Syncing files to s3 bucket #####"
# shellcheck disable=SC2154
sync_output=$(aws s3 sync ./files "s3://$s3_bucket") || die "Failed to upload files to s3 bucket"
if [[ -z "$sync_output" ]]; then
    echo "sync output is empty"
else
    echo "$sync_output"
fi